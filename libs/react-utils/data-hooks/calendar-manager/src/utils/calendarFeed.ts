import { SafeError, SafePromise, SafePromiseValue } from '@benzinga/safe-await';
import { ConferenceCallsCalendarManager } from '@benzinga/calendar-conference-calls-manager';
import { DividendsCalendarManager } from '@benzinga/calendar-dividends-manager';
import { EarningsCalendarManager } from '@benzinga/calendar-earnings-manager';
import { FdaCalendarManager } from '@benzinga/calendar-fda-manager';
import { GovernmentTradesCalendarManager } from '@benzinga/calendar-government-trades-manager';
import { GuidanceCalendarManager } from '@benzinga/calendar-guidance-manager';
import { IposCalendarManager } from '@benzinga/calendar-ipos-manager';
import { MergersAndAcquisitionsCalendarManager } from '@benzinga/calendar-ma-manager';
import { OfferingsCalendarManager } from '@benzinga/calendar-offerings-manager';
import { EconomicsCalendarManager } from '@benzinga/calendar-economics-manager';
import { RatingsCalendarManager } from '@benzinga/calendar-ratings-manager';
import { SECCalendarManager } from '@benzinga/calendar-sec-manager';
import { SplitsCalendarManager } from '@benzinga/calendar-splits-manager';
import { SquawkCalendarManager } from '@benzinga/calendar-squawk-manager';
import { ShortInterestCalendarManager } from '@benzinga/calendar-short-interest-manager';
import { OptionsActivity, SignalsCalendarManager } from '@benzinga/calendar-option-activity-manager';
import { GridTransaction } from '@benzinga/pro-ui';
import { DateRange } from './standardDates';
import { UniqueArrayBuffer, SubscriberContainer } from '@benzinga/containers';
import { Session, StockSymbol } from '@benzinga/session';
import { Quote } from '@benzinga/quotes-v3-manager';
import { Subscribable, SubscribableEventType, Subscription } from '@benzinga/subscribable';
import {
  HoldingsQuote,
  QuoteHoldingsFeed,
  QuoteHoldingsFeedEvent,
  QuotesV3HoldingsManager,
} from '@benzinga/quotes-v3-holdings-manager';
import { UnpackedArray, ValueOf } from '@benzinga/utils';
import { handleTransactionError } from './errors';

export interface DataType {
  conference: SafePromiseValue<ReturnType<ConferenceCallsCalendarManager['fetchCalendarData']>>;
  dividends: SafePromiseValue<ReturnType<DividendsCalendarManager['fetchCalendarData']>>;
  earnings: SafePromiseValue<ReturnType<EarningsCalendarManager['fetchCalendarData']>>;
  economics: SafePromiseValue<ReturnType<EconomicsCalendarManager['fetchCalendarData']>>;
  fda: SafePromiseValue<ReturnType<FdaCalendarManager['fetchCalendarData']>>;
  governmentTrades: SafePromiseValue<ReturnType<GovernmentTradesCalendarManager['fetchCalendarData']>>;
  guidance: SafePromiseValue<ReturnType<GuidanceCalendarManager['fetchCalendarData']>>;
  ipos: SafePromiseValue<ReturnType<IposCalendarManager['fetchCalendarData']>>;
  ma: SafePromiseValue<ReturnType<MergersAndAcquisitionsCalendarManager['fetchCalendarData']>>;
  offerings: SafePromiseValue<ReturnType<OfferingsCalendarManager['fetchCalendarData']>>;
  optionsActivity: SafePromiseValue<ReturnType<SignalsCalendarManager['fetchCalendarData']>>;
  ratings: SafePromiseValue<ReturnType<RatingsCalendarManager['fetchCalendarData']>>;
  sec: SafePromiseValue<ReturnType<SECCalendarManager['fetchCalendarData']>>;
  splits: SafePromiseValue<ReturnType<SplitsCalendarManager['fetchCalendarData']>>;
  shortInterest: SafePromiseValue<ReturnType<ShortInterestCalendarManager['fetchCalendarData']>>;
  squawk: SafePromiseValue<ReturnType<SquawkCalendarManager['fetchCalendarData']>>;
}

export type CalendarType = keyof DataType;

type DataHookEvent =
  | {
      items: UnpackedArray<ValueOf<DataType>>[];
      type: 'added' | 'init';
    }
  | { type: 'loading' };

export class DataHook extends Subscribable<DataHookEvent> {
  private config: ReturnType<DataHook['getConfig']>;
  private transaction?: GridTransaction<UnpackedArray<ValueOf<DataType>>>;

  private isPaused = false;
  private isLoading = true;
  private requestNumber = 0;
  private pageSize: number;
  private initPageSize: number;
  private lastUpdatedAt: number | undefined;
  private request: SafePromise<ValueOf<DataType>> | undefined;
  private container: SubscriberContainer<
    Partial<Omit<Quote, 'type'>> & UnpackedArray<ValueOf<DataType>>,
    UniqueArrayBuffer<Partial<Omit<Quote, 'type'>> & UnpackedArray<ValueOf<DataType>>>
  >;
  private containerSubscription: Subscription<typeof this.container> | undefined;
  private quotes = new Map<StockSymbol, Quote>();
  private quotesFeed: QuoteHoldingsFeed;
  private quotesFeedSubscription: Subscription<QuoteHoldingsFeed> | undefined;
  private intervalSubscription: ReturnType<typeof setInterval> | undefined;

  private quoteFields: Set<keyof HoldingsQuote>;

  constructor(
    session: Session,
    symbols: StockSymbol[] | undefined,
    calculatedDateRange: DateRange,
    quoteFields: Set<keyof HoldingsQuote>,
    calendarType: CalendarType,
    limit = 10000,
    transaction?: GridTransaction<UnpackedArray<ValueOf<DataType>>>,
    dateSearchField?: string,
  ) {
    super();
    this.transaction = transaction;
    this.pageSize = limit;
    this.initPageSize = limit < 100 ? limit : 100;
    this.quoteFields = quoteFields;
    this.container = new SubscriberContainer(limit, new UniqueArrayBuffer(q => q.id));
    this.container.clear();
    this.config = this.getConfig(session, symbols, calculatedDateRange, calendarType, dateSearchField);
    const quoteHoldingManager = session.getManager(QuotesV3HoldingsManager);
    this.quotesFeed = quoteHoldingManager.createQuotesFeed(symbols ?? [], true);
  }

  public play = () => {
    this.isPaused = false;
    this.fetchDataInternal();
  };

  public pause = () => {
    this.isPaused = true;
  };

  public fetchData = () => {
    this.fetchDataInternal();
  };

  public getItems = () => {
    return this.container.getBufferedItems();
  };

  public updateItems = items => {
    const itemsToUpdate = items.map(item => {
      const existingItem = this.container
        .getBufferedItems()
        .find(bufferedItem => (bufferedItem.ticker || bufferedItem.symbol) === item.symbol);
      return { ...existingItem, ...item };
    });

    const transaction = this.container.updateItems(
      itemsToUpdate as (Partial<Omit<Quote, 'type'>> & UnpackedArray<ValueOf<DataType>>)[],
    );

    if (transaction.transaction.add?.length) {
      this.lastUpdatedAt = transaction.transaction.add[0].updated ?? this.lastUpdatedAt;
    }
  };

  public updateSubscriptionData = (symbols: string[], fields: Set<keyof HoldingsQuote>) => {
    if (symbols.length) {
      this.quoteFields = fields;
      this.quotesFeedSubscription?.update(this.OnQuotesFeedEvent, fields);
      this.quotesFeed.addSymbols(symbols);
    }
  };

  public updateSubscriptionFields = (fields: Set<keyof HoldingsQuote>) => {
    this.quotesFeedSubscription?.update(this.OnQuotesFeedEvent, fields);
  };

  public removeSubscriptionData = (symbols: string[]) => {
    this.quotesFeed.removeSymbols(symbols);
  };

  protected onFirstSubscription() {
    this.fetchDataInternal('init');
    this.intervalSubscription = setInterval(this.fetchDataInternal, this.config.refreshSpeed);
    this.quotesFeedSubscription = this.quotesFeed.subscribe(this.OnQuotesFeedEvent, this.quoteFields);
    this.containerSubscription = this.container.subscribe(this.onContainerEvent);
  }

  protected onZeroSubscriptions() {
    this.container.clear();
    this.intervalSubscription && clearInterval(this.intervalSubscription);
    this.quotesFeedSubscription?.unsubscribe();
    this.quotesFeedSubscription = undefined;
    this.containerSubscription?.unsubscribe();
    this.containerSubscription = undefined;
  }

  private OnQuotesFeedEvent = (event: QuoteHoldingsFeedEvent) => {
    switch (event.type) {
      case 'quote-holdings:update': {
        this.quotes = new Map(this.quotesFeed.getCachedValue().map(q => [q.symbol, q]));

        const itemToUpdate = this.container
          .getBufferedItems()
          .find(bufferdItem => bufferdItem.ticker === event.quote.symbol);

        if (itemToUpdate) {
          const data = { ...itemToUpdate, ...event.quote };
          this.container.updateItems([data] as (Partial<Omit<Quote, 'type'>> & UnpackedArray<ValueOf<DataType>>)[]);
        }

        break;
      }
    }
  };

  private onContainerEvent = (event: SubscribableEventType<typeof this.container>) => {
    switch (event.type) {
      case 'update':
      case 'queued':
      case 'clear':
        this.transaction?.transaction({
          ...event,
          transaction: {
            ...event.transaction,
            addIndex: 0,
          },
        });
        break;

      case 'resumed':
        this.container.updateItems(
          this.container
            .getBufferedItems()
            .map(e => (e.ticker ? { ...this.quotes.get(e.ticker), ...e } : e)) as (Partial<Omit<Quote, 'type'>> &
            UnpackedArray<ValueOf<DataType>>)[],
        );
        break;

      case 'paused':
        break;
    }
  };

  private getConfig = (
    session: Session,
    symbols: StockSymbol[] | undefined,
    calculatedDateRange: DateRange,
    calendarType: CalendarType,
    dateSearchField?: string,
  ) => {
    const generatedParameters = {
      dateFrom: calculatedDateRange.dateStart ?? undefined,
      dateSearchField: dateSearchField,
      dateTo: calculatedDateRange.dateEnd ?? undefined,
      pageSize: this.pageSize,
    };

    switch (calendarType) {
      case 'conference':
        return {
          manager: session.getManager(ConferenceCallsCalendarManager),
          parameters: generatedParameters,
          refreshSpeed: 30000,
          symbols: symbols,
          type: calendarType,
        };
      case 'dividends':
        return {
          manager: session.getManager(DividendsCalendarManager),
          parameters: {
            dateSort: 'ex',
            display: 'flat',
            ...generatedParameters,
          },
          refreshSpeed: 30000,
          symbols: symbols,
          type: calendarType,
        };
      case 'earnings':
        return {
          manager: session.getManager(EarningsCalendarManager),
          parameters: generatedParameters,
          refreshSpeed: 30000,
          symbols: symbols,
          type: calendarType,
        };
      case 'economics':
        return {
          manager: session.getManager(EconomicsCalendarManager),
          parameters: generatedParameters,
          refreshSpeed: 30000,
          symbols: symbols,
          type: calendarType,
        };
      case 'fda':
        return {
          manager: session.getManager(FdaCalendarManager),
          parameters: generatedParameters,
          refreshSpeed: 30000,
          symbols: symbols,
          type: calendarType,
        };
      case 'governmentTrades':
        return {
          manager: session.getManager(GovernmentTradesCalendarManager),
          parameters: { ...generatedParameters, pageSize: 1000 },
          refreshSpeed: 30000,
          symbols: symbols,
          type: calendarType,
        };
      case 'guidance':
        return {
          manager: session.getManager(GuidanceCalendarManager),
          parameters: generatedParameters,
          refreshSpeed: 30000,
          symbols: symbols,
          type: calendarType,
        };
      case 'ipos':
        return {
          manager: session.getManager(IposCalendarManager),
          parameters: generatedParameters,
          refreshSpeed: 30000,
          symbols: symbols,
          type: calendarType,
        };
      case 'ma':
        return {
          manager: session.getManager(MergersAndAcquisitionsCalendarManager),
          parameters: generatedParameters,
          refreshSpeed: 30000,
          symbols: symbols,
          type: calendarType,
        };
      case 'offerings':
        return {
          manager: session.getManager(OfferingsCalendarManager),
          parameters: generatedParameters,
          refreshSpeed: 30000,
          symbols: symbols,
          type: calendarType,
        };
      case 'optionsActivity':
        return {
          dataIngress: (data: OptionsActivity[]) => data.map(d => ({ ...d, volumeAtTrade: d.volume })),
          manager: session.getManager(SignalsCalendarManager),
          parameters: generatedParameters,
          refreshSpeed: 1000,
          symbols: symbols,
          type: calendarType,
        };
      case 'ratings':
        return {
          manager: session.getManager(RatingsCalendarManager),
          parameters: {
            ...generatedParameters,
            fields:
              '*,ratings_accuracy.smart_score,ratings_accuracy.overall_success_rate,ratings_accuracy.total_ratings',
          },
          refreshSpeed: 30000,
          symbols: symbols,
          type: calendarType,
        };
      case 'sec':
        return {
          manager: session.getManager(SECCalendarManager),
          parameters: generatedParameters,
          refreshSpeed: 30000,
          symbols: symbols,
          type: calendarType,
        };
      case 'shortInterest':
        return {
          manager: session.getManager(ShortInterestCalendarManager),
          parameters: generatedParameters,
          refreshSpeed: 30000,
          symbols: symbols,
          type: calendarType,
        };
      case 'splits':
        return {
          manager: session.getManager(SplitsCalendarManager),
          parameters: generatedParameters,
          refreshSpeed: 30000,
          symbols: symbols,
          type: calendarType,
        };
      case 'squawk':
        return {
          manager: session.getManager(SquawkCalendarManager),
          parameters: generatedParameters,
          refreshSpeed: 30000,
          symbols: symbols,
          type: calendarType,
        };
    }
  };

  private fetchDataInternal = async (
    requestType: 'init' | 'historic' | 'update' = 'update',
    historicRequestNumber?: number,
  ) => {
    // if (requestType === 'init') {
    //   this.transaction?.loading();
    //   this.dispatch({
    //     type: 'loading',
    //   });
    // }
    if (
      (this.request && requestType === 'update') ||
      (this.isPaused && requestType === 'update') ||
      (requestType !== 'init' && this.isLoading === true)
    ) {
      return;
    }
    this.requestNumber = requestType === 'init' ? this.requestNumber + 1 : this.requestNumber;
    const requestId = this.requestNumber;
    if (requestType === 'historic' && (historicRequestNumber ?? 0) < this.requestNumber) {
      return;
    }
    this.isLoading = requestType === 'init';
    const parameters = { ...this.config.parameters };
    if (requestType === 'init') {
      parameters.pageSize = this.initPageSize;
    }
    this.request = this.config.manager.fetchCalendarData({
      ...parameters,
      symbols: this.config.symbols,
      updated: requestType === 'update' ? this.lastUpdatedAt : undefined,
    });

    const data = await this.request;
    if (requestId !== this.requestNumber) {
      return;
    }

    this.request = undefined;
    if (data.ok) {
      this.isLoading = false;
      const dataOk: DataType[typeof this.config.type] = data.ok;

      switch (requestType) {
        case 'update': {
          if (dataOk.length === 0) {
            return;
          }

          // for some reason when you have updated as a perimeter the data comes back in reverse order.
          const items = dataOk.reverse();

          const transaction = this.container.updateItems(
            items.map(e => (e.ticker ? { ...this.quotes.get(e.ticker), type: e.type, ...e } : e)) as (Partial<
              Omit<Quote, 'type'>
            > &
              UnpackedArray<ValueOf<DataType>>)[],
          );

          if (transaction.transaction.add) {
            this.lastUpdatedAt = transaction.transaction.add[0]?.updated ?? this.lastUpdatedAt;
          }

          this.dispatch({
            items: transaction.transaction.add ?? [],
            type: 'added',
          });
          return;
        }
        case 'init':
        case 'historic': {
          if (requestType === 'init') {
            this.transaction?.loading();
            this.dispatch({
              type: 'loading',
            });
          }
          if (dataOk.length === 0) {
            this.transaction?.noData();
            return;
          }

          if (requestType === 'historic' && (historicRequestNumber ?? 0) < this.requestNumber) {
            return;
          }

          this.container.replace(
            dataOk.map(e => (e.ticker ? { ...this.quotes.get(e.ticker), type: e.type, ...e } : e)) as (Partial<
              Omit<Quote, 'type'>
            > &
              UnpackedArray<ValueOf<DataType>>)[],
          );

          this.lastUpdatedAt = dataOk[0]?.updated ?? this.lastUpdatedAt;

          if (requestType === 'init') {
            if (dataOk.length === 0) {
              this.transaction?.noData();
              this.dispatch({
                items: [],
                type: 'init',
              });
            } else if (dataOk.length === this.initPageSize && dataOk.length < this.pageSize) {
              this.fetchDataInternal('historic', requestId);
              this.dispatch({
                items: dataOk,
                type: 'init',
              });
            } else {
              this.dispatch({
                items: dataOk,
                type: 'init',
              });
            }
          } else {
            this.dispatch({
              items: dataOk,
              type: 'init',
            });
          }

          return;
        }
      }
    } else {
      if (requestType === 'init') {
        const error = handleTransactionError(data.err as SafeError<string, Response>);
        if (error) {
          this.transaction?.error(error);
        } else {
          this.transaction?.noData();
        }
        console.error(data.err);
      }
    }
  };
}
