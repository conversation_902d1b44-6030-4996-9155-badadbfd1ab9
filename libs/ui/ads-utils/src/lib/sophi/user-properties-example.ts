/**
 * Example usage of Sophi User Properties functionality
 * 
 * This file demonstrates how to use the new user properties features
 * in the SophiManager to enrich user interaction data with business-specific
 * attributes for more effective predictions.
 */

import { sophiManager } from './SophiManager';

/**
 * Example: Setting user properties when a user subscribes to a newsletter
 */
export function handleNewsletterSubscription(newsletterName: string, userType: 'anonymous' | 'registered') {
  sophiManager.setUserProperties({
    visitorType: userType,
    1: newsletterName, // Last Newsletter Subscription (configured as property 1)
  });
}

/**
 * Example: Setting user properties when a user answers datawall questions
 */
export function handleDatawallResponse(response: string, commentCount: number) {
  sophiManager.setUserProperties({
    2: response, // Datawall Answers (configured as property 2)
    3: commentCount, // Comment Engagement Signals (configured as property 3)
  });
}

/**
 * Example: Setting user properties for known past subscribers
 */
export function markAsPastSubscriber(isPastSubscriber: boolean) {
  sophiManager.setUserProperties({
    4: isPastSubscriber, // Is a Known Past Subscriber (configured as property 4)
  });
}

/**
 * Example: Updating visitor type only
 */
export function updateVisitorType(visitorType: 'anonymous' | 'registered') {
  sophiManager.setUserProperties({
    visitorType: visitorType,
  });
}

/**
 * Example: Comprehensive user property setup during user onboarding
 */
export function setupUserProperties(userProfile: {
  isRegistered: boolean;
  lastNewsletter?: string;
  datawallResponse?: string;
  commentCount: number;
  isPastSubscriber: boolean;
}) {
  const properties: any = {
    visitorType: userProfile.isRegistered ? 'registered' : 'anonymous',
    3: userProfile.commentCount,
    4: userProfile.isPastSubscriber,
  };

  if (userProfile.lastNewsletter) {
    properties[1] = userProfile.lastNewsletter;
  }

  if (userProfile.datawallResponse) {
    properties[2] = userProfile.datawallResponse;
  }

  sophiManager.setUserProperties(properties);
}

/**
 * Example: Getting user properties for analytics tracking
 */
export function trackPageViewWithUserProperties(isArticle: boolean, section: string) {
  // Track the page view with Sophi
  sophiManager.trackPageView(isArticle, section);

  // Get user properties for additional analytics tracking
  const userProperties = sophiManager.getUserProperties();
  
  if (userProperties) {
    console.log('User properties for analytics:', userProperties);
    // You can now include userProperties in your analytics events
    // Example: gtag('event', 'page_view', { sophi_user_properties: userProperties });
  }
}

/**
 * Example: React hook for managing user properties
 */
export function useUserProperties() {
  const setNewsletterSubscription = (newsletterName: string, userType: 'anonymous' | 'registered') => {
    handleNewsletterSubscription(newsletterName, userType);
  };

  const setDatawallResponse = (response: string, commentCount: number) => {
    handleDatawallResponse(response, commentCount);
  };

  const setPastSubscriber = (isPastSubscriber: boolean) => {
    markAsPastSubscriber(isPastSubscriber);
  };

  const updateVisitor = (visitorType: 'anonymous' | 'registered') => {
    updateVisitorType(visitorType);
  };

  const getUserProperties = () => {
    return sophiManager.getUserProperties();
  };

  return {
    setNewsletterSubscription,
    setDatawallResponse,
    setPastSubscriber,
    updateVisitor,
    getUserProperties,
  };
}
